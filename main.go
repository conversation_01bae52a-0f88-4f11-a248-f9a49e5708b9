package main

import (
	"crypto/tls"
	"fmt"
	"net/http"
	"time"

	"github.com/wahjrzd/test1/hello"
)

func handler(w http.ResponseWriter, r *http.Request) {
	//fmt.Println(r.Method, r.URL, r.Proto)
	//fmt.Fprintf(w, "欢迎访问Go HTTP服务!\n")
	//fmt.Fprintf(w, " %s\n", r.URL.Path)
	w.WriteHeader(200)
}

const (
	concurrency = 300                               // goroutine 数量
	interval    = 200 * time.Millisecond            // 每个 goroutine 请求间隔
	targetURL   = "https://**************:8443/get" // 目标 URL
)

func worker(id int) {
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	client := &http.Client{Transport: tr}

	for {
		resp, err := client.Get(targetURL)
		if err != nil {
			fmt.Printf("Goroutine %d 请求失败: %v\n", id, err)
		} else {
			fmt.Printf("Goroutine %d 收到响应: %d\n", id, resp.StatusCode)
			resp.Body.Close()
		}
		time.Sleep(interval)
	}
}

func main() {
	for i := 0; i < concurrency; i++ {
		go worker(i)
	}
	//f, e := os.Create()
	hello.Hello()
	fmt.Println("Hello, World!")

	http.HandleFunc("/", handler)
	// 设置服务器端口
	port := ":8081"
	fmt.Printf("HTTP服务正在监听端口 %s...\n", port)

	// 启动HTTP服务器
	err := http.ListenAndServe(port, nil)
	if err != nil {
		fmt.Printf("启动服务器时出错: %v\n", err)
	}
}
